#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试增强分析系统的详细分析结果功能
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 创建测试数据
def create_test_data():
    """创建测试数据"""
    data = {
        'Depth(m)': [0.0, 0.1, 0.2, 0.3, 0.4],
        '1-2 Speed%': [95.0, 88.0, 78.0, 92.0, 85.0],
        '1-2 Amp%': [2.5, 4.5, 8.5, 3.0, 5.5],
        '1-3 Speed%': [98.0, 82.0, 76.0, 94.0, 87.0],
        '1-3 Amp%': [1.8, 5.2, 9.8, 2.8, 6.2],
        '2-3 Speed%': [96.0, 85.0, 80.0, 93.0, 86.0],
        '2-3 Amp%': [2.2, 4.8, 7.5, 3.2, 5.8],
        '1-2 Energy%': [0.85, 0.65, 0.35, 0.82, 0.68],
        '1-3 Energy%': [0.88, 0.62, 0.32, 0.85, 0.65],
        '2-3 Energy%': [0.86, 0.68, 0.38, 0.83, 0.67],
        '1-2 PSD': [0.8, 1.5, 2.5, 0.9, 1.8],
        '1-3 PSD': [0.7, 1.8, 2.8, 0.8, 1.6],
        '2-3 PSD': [0.9, 1.6, 2.6, 0.85, 1.7]
    }
    
    df = pd.DataFrame(data)
    return df

def test_enhanced_analysis():
    """测试增强分析功能"""
    print("开始测试增强分析系统...")
    
    # 创建测试数据
    test_df = create_test_data()
    print(f"创建测试数据: {test_df.shape}")
    print(test_df.head())
    
    # 保存测试数据到文件
    test_file = "test_pile_data.txt"
    test_df.to_csv(test_file, sep='\t', index=False)
    print(f"测试数据已保存到: {test_file}")
    
    print("\n请按以下步骤测试:")
    print("1. 运行 Pile_analyze_GZ_gui_final_M.py")
    print("2. 加载测试数据文件: test_pile_data.txt")
    print("3. 启用增强分析系统")
    print("4. 运行分析，查看详细分析结果")
    print("5. 验证每个深度和剖面的判别原因是否正确显示")
    
    print("\n预期结果格式:")
    print("详细分析结果:")
    print("-" * 50)
    print("深度 0.00m: 轻微畸变 (存在1个剖面(33.3%)为轻微畸变，取最严重分类)")
    print("  剖面1-2: 轻微畸变 (声速95.0%属于轻微畸变范围(85.0-100.0%))")
    print("  剖面1-3: 正常 (声速98.0%属于正常范围(100.0-1000.0%))")
    print("  剖面2-3: 轻微畸变 (声速96.0%属于轻微畸变范围(85.0-100.0%))")
    print("  连续长度: 0.10m")
    print("  轻微畸变横向比例: 66.7%")
    print()

if __name__ == "__main__":
    test_enhanced_analysis()
